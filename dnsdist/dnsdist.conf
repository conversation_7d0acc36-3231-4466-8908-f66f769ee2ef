-- Define the backend server pool.
-- 'pdns-recursor' is the service name from docker-compose.yml.
newServer({address="pdns-recursor:53"})

-- Listen for incoming DNS queries on all interfaces, port 53.
addLocal("0.0.0.0:53")
addLocal("[::]:53")

-- Enable the web interface for statistics and control.
-- The password should be changed to a secure value.
webserver("0.0.0.0:8083", "your_web_password")

-- Enable the control socket for the dnsdist-console utility.
controlSocket("0.0.0.0:5199")
setKey("your_console_key")
