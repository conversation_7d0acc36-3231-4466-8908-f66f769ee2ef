# Launch the Generic PostgreSQL backend
launch=gpgsql

# PostgreSQL connection settings
# Use the Docker Compose service name for the host.
gpgsql-host=pdns-db
gpgsql-port=5432
gpgsql-dbname=powerdns
gpgsql-user=powerdns
gpgsql-password=powerdns

# Enable the HTTP API and Webserver for management
api=yes
api-key=your_very_secure_api_key
webserver=yes
webserver-address=0.0.0.0
webserver-port=8081
# Allow API access from any IP (access should be controlled by host firewall)
webserver-allow-from=0.0.0.0/0,::/0

# Logging settings
log-dns-queries=yes
loglevel=4
